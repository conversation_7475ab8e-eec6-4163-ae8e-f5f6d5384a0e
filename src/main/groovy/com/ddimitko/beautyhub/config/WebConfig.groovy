package com.ddimitko.beautyhub.config

import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
class WebConfig implements WebMvcConfigurer {

    @Override
    void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Serve uploaded files from the uploads directory
        String projectRoot = System.getProperty("user.dir")
        String uploadsPath = "file:${projectRoot}/uploads/"

        registry.addResourceHandler("/uploads/**")
                .addResourceLocations(uploadsPath)
                .setCachePeriod(3600) // Cache for 1 hour

        // Add handler for legacy image URLs (for backward compatibility)
        registry.addResourceHandler("/uploads/shop_*.jpg", "/uploads/shop_*.png", "/uploads/shop_*.gif", "/uploads/shop_*.webp")
                .addResourceLocations(uploadsPath)
                .setCachePeriod(3600)
    }
}
