package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.ShopCreationRequest
import com.ddimitko.beautyhub.dto.ShopCreationResponse
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.enums.BusinessType
import com.ddimitko.beautyhub.repository.ShopRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class ShopService {

    @Autowired
    private ShopRepository shopRepository

    @Autowired
    private UserService userService

    @Autowired
    private StripeService stripeService

    Shop createShop(UUID ownerId, ShopCreationRequest request) {
        User owner = userService.findById(ownerId)

        // Upgrade user to OWNER role if they're creating a shop
        if (!owner.isOwner()) {
            owner = userService.upgradeToOwner(ownerId)
        }

        // Check if owner already has a shop (optional business rule)
        List<Shop> existingShops = shopRepository.findByOwner(owner)
        if (!existingShops.isEmpty()) {
            throw new IllegalArgumentException("You already have a shop. Multiple shops per owner not currently supported.")
        }

        Shop shop = new Shop()
        shop.owner = owner
        shop.name = request.name
        shop.description = request.description
        shop.businessTypes = request.businessTypes
        shop.address = request.address
        shop.city = request.city
        shop.state = request.state
        shop.postalCode = request.postalCode
        shop.country = request.country
        shop.phone = request.phone
        shop.email = request.email
        shop.website = request.website
        shop.acceptsCardPayments = request.acceptsCardPayments ?: false
        shop.active = true

        return shopRepository.save(shop)
    }

    ShopCreationResponse createShopWithResponse(UUID ownerId, ShopCreationRequest request) {
        // Validate terms acceptance
        if (!request.termsAccepted) {
            throw new IllegalArgumentException("Terms and conditions must be accepted")
        }

        // For now, we'll create the shop immediately but mark it as requiring subscription
        // In a real implementation, you might want to create a "pending" shop that gets activated after subscription
        Shop shop = createShop(ownerId, request)

        ShopCreationResponse response = new ShopCreationResponse()
        response.shopId = shop.id
        response.message = "Shop information saved! Please complete your subscription to activate your account."
        response.name = shop.name
        response.address = shop.getFullAddress()
        response.requiresStripeSetup = shop.acceptsCardPayments && !shop.stripeAccountId
        response.requiresSubscription = true // Always require subscription for new shops
        response.subscriptionActive = false

        // Always require subscription for new professional accounts
        response.nextStep = "subscription_setup"

        return response
    }

    Shop findById(UUID id) {
        Shop shop = shopRepository.findById(id)
                .orElseThrow { new RuntimeException("Shop not found with id: $id") }

        // Migrate gallery URLs if needed
        return migrateGalleryUrls(shop)
    }

    List<Shop> findByOwner(User owner) {
        return shopRepository.findByOwner(owner)
    }

    List<Shop> findByOwnerId(UUID ownerId) {
        User owner = userService.findById(ownerId)
        return shopRepository.findByOwner(owner)
    }

    List<Shop> findActiveShops() {
        return shopRepository.findByActiveTrue()
    }

    Page<Shop> findActiveShops(Pageable pageable) {
        return shopRepository.findByActiveTrue(pageable)
    }

    List<Shop> findByBusinessType(BusinessType businessType) {
        return shopRepository.findByBusinessTypesContaining(businessType)
    }

    Page<Shop> searchShops(String name, String city, Double minRating, Boolean acceptsCard, Pageable pageable) {
        return shopRepository.findShopsWithFilters(name, city, minRating, acceptsCard, pageable)
    }

    Shop updateShop(UUID id, ShopCreationRequest request) {
        Shop shop = findById(id)
        
        if (request.name) shop.name = request.name
        if (request.description) shop.description = request.description
        if (request.businessTypes) shop.businessTypes = request.businessTypes
        if (request.address) shop.address = request.address
        if (request.city) shop.city = request.city
        if (request.state) shop.state = request.state
        if (request.postalCode) shop.postalCode = request.postalCode
        if (request.country) shop.country = request.country
        if (request.phone) shop.phone = request.phone
        if (request.email) shop.email = request.email
        if (request.website) shop.website = request.website
        if (request.acceptsCardPayments != null) shop.acceptsCardPayments = request.acceptsCardPayments

        return shopRepository.save(shop)
    }

    void deleteShop(UUID id) {
        Shop shop = findById(id)
        shop.active = false
        shopRepository.save(shop)
    }

    long countShopsByOwner(User owner) {
        return shopRepository.countByOwner(owner)
    }

    boolean hasShop(User owner) {
        return countShopsByOwner(owner) > 0
    }

    /**
     * Migrate gallery URLs from old format to new format
     * Old format: /uploads/shop_{shopId}_{timestamp}.{ext}
     * New format: /uploads/shops/{shopId}/{timestamp}.{ext}
     */
    private Shop migrateGalleryUrls(Shop shop) {
        boolean needsUpdate = false

        // Migrate gallery URLs
        if (shop.gallery) {
            List<String> migratedGallery = []
            for (String imageUrl : shop.gallery) {
                String migratedUrl = migrateImageUrl(imageUrl, shop.id)
                migratedGallery.add(migratedUrl)
                if (migratedUrl != imageUrl) {
                    needsUpdate = true
                }
            }
            shop.gallery = migratedGallery
        }

        // Migrate thumbnail URL
        if (shop.thumbnail) {
            String migratedThumbnail = migrateImageUrl(shop.thumbnail, shop.id)
            if (migratedThumbnail != shop.thumbnail) {
                shop.thumbnail = migratedThumbnail
                needsUpdate = true
            }
        }

        // Save if URLs were migrated
        if (needsUpdate) {
            println "Migrating gallery URLs for shop ${shop.id}"
            shop = shopRepository.save(shop)
        }

        return shop
    }

    private String migrateImageUrl(String imageUrl, UUID shopId) {
        if (!imageUrl || !imageUrl.startsWith('/uploads/shop_')) {
            return imageUrl // Already in new format or not a shop image
        }

        // Extract timestamp and extension from old format
        // Old format: /uploads/shop_{shopId}_{timestamp}.{ext}
        String pattern = /^\/uploads\/shop_[a-f0-9-]+_(.+)$/
        def matcher = imageUrl =~ pattern

        if (matcher.matches()) {
            String timestampAndExt = matcher[0][1]
            // New format: /uploads/shops/{shopId}/{timestamp}.{ext}
            return "/uploads/shops/${shopId}/${timestampAndExt}"
        }

        return imageUrl // Return original if pattern doesn't match
    }
}
