package com.ddimitko.beautyhub.controller

import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RestController

import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.util.regex.Pattern

@RestController
class LegacyImageController {

    /**
     * Handle legacy image URLs and redirect/serve them from the new location
     * Old format: /uploads/shop_{shopId}_{timestamp}.{ext}
     * New format: /uploads/shops/{shopId}/{timestamp}.{ext}
     */
    @GetMapping("/uploads/shop_{shopId}_{filename}")
    ResponseEntity<Resource> serveLegacyImage(
            @PathVariable("shopId") String shopId,
            @PathVariable("filename") String filename) {
        
        try {
            println "=== LEGACY IMAGE REQUEST ==="
            println "Shop ID: ${shopId}"
            println "Filename: ${filename}"
            
            // Get project root
            String projectRoot = System.getProperty("user.dir")
            
            // Try new location first: /uploads/shops/{shopId}/{filename}
            Path newPath = Paths.get(projectRoot, "uploads", "shops", shopId, filename)
            println "Trying new path: ${newPath}"
            
            if (Files.exists(newPath)) {
                println "Found in new location"
                return serveImageFile(newPath)
            }
            
            // Try old location: /uploads/shop_{shopId}_{filename}
            String oldFilename = "shop_${shopId}_${filename}"
            Path oldPath = Paths.get(projectRoot, "uploads", oldFilename)
            println "Trying old path: ${oldPath}"
            
            if (Files.exists(oldPath)) {
                println "Found in old location"
                return serveImageFile(oldPath)
            }
            
            println "Image not found in either location"
            return ResponseEntity.notFound().build()
            
        } catch (Exception e) {
            println "Error serving legacy image: ${e.getMessage()}"
            e.printStackTrace()
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }
    
    private ResponseEntity<Resource> serveImageFile(Path imagePath) {
        try {
            Resource resource = new FileSystemResource(imagePath)
            
            if (!resource.exists()) {
                return ResponseEntity.notFound().build()
            }
            
            // Determine content type
            String contentType = Files.probeContentType(imagePath)
            if (contentType == null) {
                contentType = "application/octet-stream"
            }
            
            HttpHeaders headers = new HttpHeaders()
            headers.setContentType(MediaType.parseMediaType(contentType))
            headers.setCacheControl("max-age=3600") // Cache for 1 hour
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource)
                    
        } catch (Exception e) {
            println "Error serving image file: ${e.getMessage()}"
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }
}
