package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.UserProfileUpdateRequest
import com.ddimitko.beautyhub.dto.ChangePasswordRequest
import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.UserService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

import jakarta.validation.Valid

@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*", maxAge = 3600)
class UserController {

    @Autowired
    private UserService userService

    @GetMapping("/profile")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> getUserProfile(@AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            User user = userService.findById(userPrincipal.getId())
            
            return ResponseEntity.ok([
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                avatar: user.avatar,
                role: user.role.name(),
                emailVerified: user.emailVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve user profile",
                message: e.getMessage()
            ])
        }
    }

    @PutMapping("/profile")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> updateUserProfile(
            @Valid @RequestBody UserProfileUpdateRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            User updatedUser = new User()
            updatedUser.firstName = request.firstName
            updatedUser.lastName = request.lastName
            updatedUser.phone = request.phone
            updatedUser.avatar = request.avatar

            User user = userService.updateUser(userPrincipal.getId(), updatedUser)
            
            return ResponseEntity.ok([
                message: "Profile updated successfully",
                user: [
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: user.phone,
                    avatar: user.avatar,
                    role: user.role.name(),
                    emailVerified: user.emailVerified,
                    updatedAt: user.updatedAt
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Profile update failed",
                message: e.getMessage()
            ])
        }
    }

    @PutMapping("/change-password")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            userService.changePassword(
                userPrincipal.getId(),
                request.currentPassword,
                request.newPassword
            )

            return ResponseEntity.ok([
                message: "Password changed successfully"
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Password change failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Password change failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    @PostMapping("/avatar")
    @PreAuthorize("hasAnyRole('USER', 'EMPLOYEE', 'OWNER')")
    ResponseEntity<?> uploadAvatar(
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            println "=== AVATAR UPLOAD DEBUG ==="
            println "File: ${file?.originalFilename}, Size: ${file?.size}, Type: ${file?.contentType}"
            println "User: ${userPrincipal?.email}"

            // Validate file
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body([
                    error: "No file provided"
                ])
            }

            // Validate file type
            String contentType = file.getContentType()
            if (!contentType?.startsWith("image/")) {
                return ResponseEntity.badRequest().body([
                    error: "Only image files are allowed"
                ])
            }

            // Validate file size (max 2MB for avatars)
            if (file.getSize() > 2 * 1024 * 1024) {
                return ResponseEntity.badRequest().body([
                    error: "File size must be less than 2MB"
                ])
            }

            // Generate filename using user ID
            String originalFilename = file.getOriginalFilename()
            String extension = originalFilename?.substring(originalFilename.lastIndexOf('.')) ?: '.jpg'
            String filename = "${userPrincipal.getId()}${extension}"

            // Get the absolute path to the project root uploads directory with avatars folder
            String projectRoot = System.getProperty("user.dir")
            File uploadsDir = new File(projectRoot, "uploads")
            File avatarsDir = new File(uploadsDir, "avatars")

            println "=== AVATAR UPLOAD PATHS ==="
            println "Project root: ${projectRoot}"
            println "Avatars dir: ${avatarsDir.absolutePath}"
            println "Filename: ${filename}"

            // Create avatars directory if it doesn't exist
            if (!avatarsDir.exists()) {
                boolean created = avatarsDir.mkdirs()
                println "Created avatars directory: ${created}"
            }

            // Delete existing avatar if it exists
            File[] existingFiles = avatarsDir.listFiles { dir, name ->
                name.startsWith(userPrincipal.getId().toString())
            }
            existingFiles?.each { it.delete() }

            // Save file
            File destinationFile = new File(avatarsDir, filename)
            println "Destination file: ${destinationFile.absolutePath}"
            file.transferTo(destinationFile)

            // Generate URL
            String avatarUrl = "/uploads/avatars/${filename}"

            // Update user avatar
            User updatedUser = new User()
            updatedUser.avatar = avatarUrl
            User user = userService.updateUser(userPrincipal.getId(), updatedUser)

            return ResponseEntity.ok([
                message: "Avatar uploaded successfully",
                avatarUrl: avatarUrl,
                user: [
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: user.phone,
                    avatar: user.avatar,
                    role: user.role.name(),
                    emailVerified: user.emailVerified,
                    updatedAt: user.updatedAt
                ]
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Avatar upload failed",
                message: e.getMessage()
            ])
        }
    }
}
