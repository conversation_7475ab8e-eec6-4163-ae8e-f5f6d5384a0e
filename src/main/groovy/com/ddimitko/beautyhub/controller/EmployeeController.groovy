package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.EmployeeCreationRequest
import com.ddimitko.beautyhub.dto.EmployeeCreationWithUserRequest
import com.ddimitko.beautyhub.dto.EmployeeInvitationRequest
import com.ddimitko.beautyhub.entity.Employee
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.repository.EmployeeRepository
import com.ddimitko.beautyhub.service.EmployeeService
import com.ddimitko.beautyhub.service.ShopService
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/employees")
@CrossOrigin(origins = "*", maxAge = 3600)
class EmployeeController {

    @Autowired
    private EmployeeService employeeService

    @Autowired
    private ShopService shopService

    @Autowired
    private EmployeeRepository employeeRepository

    @PostMapping("/shops/{shopId}/invite")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> inviteEmployee(
            @PathVariable("shopId") UUID shopId,
            @Valid @RequestBody EmployeeInvitationRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only manage employees for your own shops"
                ])
            }

            Employee employee = employeeService.inviteUserAsEmployee(shopId, request.email, request)
            return ResponseEntity.ok([
                message: "Employee invited successfully",
                employeeId: employee.id,
                employeeName: employee.fullName,
                email: employee.email
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Employee invitation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Employee invitation failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    @PostMapping("/shops/{shopId}/create")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> createEmployee(
            @PathVariable("shopId") UUID shopId,
            @Valid @RequestBody EmployeeCreationWithUserRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only create employees for your own shops"
                ])
            }

            Employee employee = employeeService.createEmployeeWithNewUser(shopId, request)
            return ResponseEntity.ok([
                message: "Employee created successfully",
                employeeId: employee.id,
                employeeName: employee.fullName,
                email: employee.email
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Employee creation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Employee creation failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    @GetMapping("/shops/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> getShopEmployees(
            @PathVariable("shopId") UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only view employees for your own shops"
                ])
            }

            List<Employee> employees = employeeService.getActiveEmployeesByShop(shopId)
            return ResponseEntity.ok(employees.collect { employee ->
                [
                    id: employee.id,
                    name: employee.fullName,
                    email: employee.email,
                    phone: employee.phone,
                    bio: employee.bio,
                    specialties: employee.specialties,
                    yearsExperience: employee.yearsExperience,
                    hourlyRate: employee.hourlyRate,
                    commissionRate: employee.commissionRate,
                    hireDate: employee.hireDate,
                    active: employee.active,
                    avatar: employee.user?.avatar
                ]
            })
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build()
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve employees",
                message: "An unexpected error occurred"
            ])
        }
    }

    @PutMapping("/{employeeId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> updateEmployee(
            @PathVariable("employeeId") UUID employeeId,
            @Valid @RequestBody EmployeeCreationRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            Employee employee = employeeService.updateEmployee(employeeId, request)
            
            // Verify that the user owns the shop where this employee works
            if (!employee.shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only update employees for your own shops"
                ])
            }

            return ResponseEntity.ok([
                message: "Employee updated successfully",
                employeeId: employee.id,
                employeeName: employee.fullName
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build()
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Employee update failed",
                message: e.getMessage()
            ])
        }
    }

    @DeleteMapping("/{employeeId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> deactivateEmployee(
            @PathVariable("employeeId") UUID employeeId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Get the employee to check ownership
            Employee employee = employeeRepository.findById(employeeId)
                    .orElseThrow { new RuntimeException("Employee not found") }

            if (!employee.shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only deactivate employees for your own shops"
                ])
            }

            employeeService.deactivateEmployee(employeeId)
            return ResponseEntity.ok([
                message: "Employee deactivated successfully"
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build()
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Employee deactivation failed",
                message: e.getMessage()
            ])
        }
    }

    @GetMapping("/my-profiles")
    @PreAuthorize("hasAnyRole('EMPLOYEE', 'OWNER')")
    ResponseEntity<?> getMyEmployeeProfiles(@AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            List<Employee> employees = employeeService.getEmployeesByUser(userPrincipal.getId())
            if (employees.isEmpty()) {
                return ResponseEntity.notFound().body([
                    error: "Employee profiles not found",
                    message: "You don't have any employee profiles"
                ])
            }

            return ResponseEntity.ok(employees.collect { employee ->
                [
                    id: employee.id,
                    shopId: employee.shop.id,
                    shopName: employee.shop.name,
                    bio: employee.bio,
                    specialties: employee.specialties,
                    yearsExperience: employee.yearsExperience,
                    hourlyRate: employee.hourlyRate,
                    commissionRate: employee.commissionRate,
                    hireDate: employee.hireDate,
                    active: employee.active,
                    avatar: employee.user?.avatar
                ]
            })
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve employee profiles",
                message: "An unexpected error occurred"
            ])
        }
    }

    @GetMapping("/my-shops")
    @PreAuthorize("hasAnyRole('EMPLOYEE', 'OWNER')")
    ResponseEntity<?> getMyEmployeeShops(@AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            List<Shop> shops = employeeService.getShopsWhereUserIsEmployee(userPrincipal.getId())
            return ResponseEntity.ok(shops.collect { shop ->
                [
                    id: shop.id,
                    name: shop.name,
                    address: shop.getFullAddress(),
                    phone: shop.phone,
                    email: shop.email
                ]
            })
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve employee shops",
                message: "An unexpected error occurred"
            ])
        }
    }
}
