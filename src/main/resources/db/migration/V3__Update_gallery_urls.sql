-- Update gallery URLs to new folder structure
-- This migration updates existing gallery URLs from the old format to the new format

-- First, let's see what we're working with (this is just for documentation)
-- Old format: /uploads/shop_{shopId}_{timestamp}.{ext}
-- New format: /uploads/shops/{shopId}/{timestamp}.{ext}

-- Update gallery URLs that follow the old pattern
UPDATE shop_gallery 
SET image_url = CONCAT(
    '/uploads/shops/',
    SUBSTRING(image_url FROM 'shop_([a-f0-9-]+)_'),
    '/',
    SUBSTRING(image_url FROM '_([0-9]+\.[a-z]+)$')
)
WHERE image_url LIKE '/uploads/shop_%'
AND image_url ~ '^/uploads/shop_[a-f0-9-]+_[0-9]+\.[a-z]+$';

-- Update thumbnail URLs in shops table that follow the old pattern
UPDATE shops 
SET thumbnail = CONCAT(
    '/uploads/shops/',
    SUBSTRING(thumbnail FROM 'shop_([a-f0-9-]+)_'),
    '/',
    SUBSTRING(thumbnail FROM '_([0-9]+\.[a-z]+)$')
)
WHERE thumbnail LIKE '/uploads/shop_%'
AND thumbnail ~ '^/uploads/shop_[a-f0-9-]+_[0-9]+\.[a-z]+$';
