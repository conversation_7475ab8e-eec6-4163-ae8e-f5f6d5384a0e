import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Star, MapPin, Clock, Phone, Globe, Calendar } from 'lucide-react'
import { shopAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import ImageCarousel from '../ui/ImageCarousel'
import { Badge } from '../ui/Badge'

const ShopPage = () => {
  const { id } = useParams()

  // Fetch shop data from API
  const { data: shopData, isLoading, error } = useQuery({
    queryKey: ['shop', id],
    queryFn: () => shopAPI.getShop(id),
    enabled: !!id
  })

  // Fetch shop services separately (using public endpoint)
  const { data: servicesData, isLoading: servicesLoading } = useQuery({
    queryKey: ['shopServices', id],
    queryFn: () => shopAPI.getShopServicesPublic(id),
    enabled: !!id
  })

  // Fetch shop hours (aggregated from employee schedules)
  const { data: hoursData, isLoading: hoursLoading } = useQuery({
    queryKey: ['shopHours', id],
    queryFn: () => shopAPI.getShopHours(id),
    enabled: !!id
  })

  const shop = shopData?.data
  const services = Array.isArray(servicesData?.data?.data) ? servicesData.data.data : []
  const shopHours = hoursData?.data?.data || []

  // Helper function to format business types for display
  const formatBusinessType = (type) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  // Helper function to format hours
  const formatHours = (hours) => {
    if (!hours) return "Hours not available"
    return `${hours.openTime} - ${hours.closeTime}`
  }

  if (isLoading || servicesLoading || hoursLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded-lg mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
              <div className="space-y-4">
                <div className="h-32 bg-gray-200 rounded"></div>
                <div className="h-48 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="text-center p-8">
          <CardContent>
            <h2 className="text-2xl font-bold mb-4">Shop Not Found</h2>
            <p className="text-gray-600 mb-4">The shop you're looking for doesn't exist.</p>
            <Button asChild className="bg-gray-900 hover:bg-gray-800 text-white">
              <Link to="/search">Browse Other Shops</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Image/Gallery */}
        <ImageCarousel
          images={shop.gallery || []}
          alt={shop.name}
          className="mb-8"
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Shop Info */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-3xl mb-2">{shop.name}</CardTitle>
                    <div className="flex items-center space-x-4 text-gray-600">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                        <span className="font-medium">{shop.ratingAverage?.toFixed(1) || 'N/A'}</span>
                        <span className="ml-1">({shop.ratingCount || 0} reviews)</span>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>{shop.address}, {shop.city}, {shop.state}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {shop.businessTypes?.map(type => (
                      <Badge key={type} variant="secondary">
                        {formatBusinessType(type)}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{shop.description || "Welcome to our beauty shop! We offer professional services to help you look and feel your best."}</p>
              </CardContent>
            </Card>

            {/* Services */}
            <Card>
              <CardHeader>
                <CardTitle>Services & Pricing</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {services.length > 0 ? (
                    services.map((service) => (
                      <div key={service.id} className="flex justify-between items-center p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{service.name}</h4>
                          <p className="text-sm text-gray-600">{service.durationMinutes} minutes</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">${service.price}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No services listed yet.</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Reviews */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {shop.reviews?.length > 0 ? (
                    shop.reviews.map(review => (
                      <div key={review.id} className="border-b pb-4 last:border-b-0">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <p className="font-medium">{review.customerName || 'Anonymous'}</p>
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                          <span className="text-sm text-gray-500">{new Date(review.createdAt).toLocaleDateString()}</span>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No reviews yet.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Book Appointment */}
            <Card>
              <CardHeader>
                <CardTitle>Book Appointment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button asChild className="w-full bg-gray-900 hover:bg-gray-800 text-white" size="lg">
                  <Link to={`/shop/${shop.id}/book`}>
                    <Calendar className="mr-2 h-4 w-4" />
                    Book Now
                  </Link>
                </Button>
                <div className="text-center text-sm text-gray-600">
                  Next available: Today 2:00 PM
                </div>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{shop.phone || 'Phone not available'}</span>
                </div>
                {shop.website && (
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 mr-2 text-gray-400" />
                    <a href={`https://${shop.website}`} className="text-primary hover:underline">
                      {shop.website}
                    </a>
                  </div>
                )}
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-0.5" />
                  <span>{shop.address}, {shop.city}, {shop.state} {shop.zipCode}</span>
                </div>
              </CardContent>
            </Card>

            {/* Hours */}
            <Card>
              <CardHeader>
                <CardTitle>Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {shopHours.length > 0 ? (
                    shopHours.map((hours, index) => (
                      <div key={index} className="flex justify-between">
                        <span className="capitalize font-medium">{hours.dayOfWeek.toLowerCase()}</span>
                        <span className="text-gray-600">
                          {hours.closed ? 'Closed' : `${hours.openTime} - ${hours.closeTime}`}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-2">Hours not available</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ShopPage
