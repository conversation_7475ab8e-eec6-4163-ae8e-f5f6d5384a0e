import axios from 'axios'
import { API_BASE_URL } from './utils'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from Zustand store
    try {
      const authStore = require('../store/authStore').default.getState()
      const token = authStore.token
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      console.error('Error getting token from store:', error)
      // Fallback to localStorage for backward compatibility during transition
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Track if we're currently refreshing to prevent multiple refresh attempts
let isRefreshing = false
let failedQueue = []

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error)
    } else {
      prom.resolve(token)
    }
  })

  failedQueue = []
}

// Response interceptor to handle auth errors and token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    // Don't try to refresh if this is already a refresh request or if we've already tried
    if (error.response?.status === 401 &&
        !originalRequest._retry &&
        !originalRequest.url?.includes('/auth/refresh')) {

      if (isRefreshing) {
        // If we're already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return api(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        // Try to refresh token
        const refreshResponse = await api.post('/auth/refresh')
        const { token, id, email, firstName, lastName, avatar, roles } = refreshResponse.data

        // Update token in store
        try {
          const authStore = require('../store/authStore').default.getState()
          authStore.login({ id, email, firstName, lastName, avatar, roles }, token)
        } catch (error) {
          console.error('Error updating store after refresh:', error)
          // Fallback to localStorage
          localStorage.setItem('token', token)
        }

        // Update the original request with new token
        originalRequest.headers.Authorization = `Bearer ${token}`

        // Process queued requests
        processQueue(null, token)

        // Retry the original request
        return api(originalRequest)
      } catch (refreshError) {
        // Process queued requests with error
        processQueue(refreshError, null)

        // Refresh failed, logout user
        try {
          const authStore = require('../store/authStore').default.getState()
          authStore.logout()
        } catch (error) {
          console.error('Error logging out from store:', error)
          // Fallback to manual cleanup
          localStorage.removeItem('token')
          localStorage.removeItem('user')
        }

        // Only redirect if not already on login page and not in shop creation flow
        if (window.location.pathname !== '/login' && !window.location.pathname.includes('/dashboard')) {
          window.location.href = '/login'
        }

        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  refreshToken: () => api.post('/auth/refresh'),
  validateToken: () => api.post('/auth/validate'),
}

// User API
export const userAPI = {
  getProfile: () => api.get('/user/profile'),
  updateProfile: (userData) => api.put('/user/profile', userData),
  changePassword: (passwordData) => api.put('/user/change-password', passwordData),
  uploadAvatar: (formData) => api.post('/user/avatar', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
}

// Shop API
export const shopAPI = {
  getShops: (params) => api.get('/public/shops', { params }),
  getShop: (id) => api.get(`/public/shops/${id}`),
  createShop: (shopData) => api.post('/shops', shopData),
  updateShop: (id, shopData) => api.put(`/shops/${id}`, shopData),
  deleteShop: (id) => api.delete(`/shops/${id}`),
  getShopServices: (id) => api.get(`/shops/${id}/services`), // Authenticated endpoint for shop management
  getShopServicesPublic: (id) => api.get(`/public/shops/${id}/services`), // Public endpoint for appointment booking
  getShopEmployees: (id) => api.get(`/public/shops/${id}/employees`), // Public endpoint for appointment booking
  getShopHours: (id) => api.get(`/public/shops/${id}/hours`), // Public endpoint for shop hours
  getShopRatings: (id) => api.get(`/shops/${id}/ratings`),
  getMyShops: () => api.get('/shops/my-shops'),
  getOwnerShops: () => api.get('/shops/my-shops'), // Alias for owner-specific context
  getShopsByBusinessType: (businessType) => api.get(`/shops/by-type/${businessType}`),
  getBusinessTypes: () => api.get('/shops/business-types'),
  // Gallery management
  uploadGalleryImage: (shopId, formData) => api.post(`/shops/${shopId}/gallery`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteGalleryImage: (shopId, imageUrl) => api.delete(`/shops/${shopId}/gallery`, { data: { imageUrl } }),
  setThumbnail: (shopId, imageUrl) => api.put(`/shops/${shopId}/thumbnail`, { imageUrl }),
}

// Service API
export const serviceAPI = {
  getServices: (params) => api.get('/services', { params }),
  getService: (id) => api.get(`/services/${id}`),
  createService: (serviceData) => api.post('/services', serviceData),
  updateService: (id, serviceData) => api.put(`/services/${id}`, serviceData),
  deleteService: (id) => api.delete(`/services/${id}`),
}

// Appointment API
export const appointmentAPI = {
  getAppointments: (params) => api.get('/appointments', { params }),
  getAppointment: (id) => api.get(`/appointments/${id}`),
  createAppointment: (appointmentData) => api.post('/appointments', appointmentData),
  updateAppointment: (id, appointmentData) => api.put(`/appointments/${id}`, appointmentData),
  cancelAppointment: (id, reason) => api.put(`/appointments/${id}/cancel`, { reason }),
  getAvailableSlots: (params) => api.get('/appointments/available-slots', { params }),
  lockSlot: (slotData) => api.post('/appointments/lock-slot', slotData),
  unlockSlot: (slotData) => api.post('/appointments/unlock-slot', slotData),
  releasePaymentLock: (slotData) => api.post('/appointments/release-payment-lock', slotData),
  getLockToken: (slotData) => api.post('/appointments/get-lock-token', slotData),
  getMyAppointments: (params) => api.get('/appointments/my-appointments', { params }),
  getShopAppointments: (shopId, params) => api.get(`/appointments/shop/${shopId}`, { params }),
  getEmployeeAppointments: (employeeId, params) => api.get(`/appointments/employee/${employeeId}`, { params }),
}

// Employee API
export const employeeAPI = {
  inviteEmployee: (shopId, employeeData) => api.post(`/employees/shops/${shopId}/invite`, employeeData),
  createEmployee: (shopId, employeeData) => api.post(`/employees/shops/${shopId}/create`, employeeData),
  getShopEmployees: (shopId) => api.get(`/employees/shops/${shopId}`),
  updateEmployee: (employeeId, employeeData) => api.put(`/employees/${employeeId}`, employeeData),
  deactivateEmployee: (employeeId) => api.delete(`/employees/${employeeId}`),
  getMyEmployeeProfile: () => api.get('/employees/my-profile'),
  getMyEmployeeShops: () => api.get('/employees/my-shops'),
}

// Schedule API
export const scheduleAPI = {
  getEmployeeSchedule: (employeeId) => api.get(`/schedules/employee/${employeeId}`),
  getPublicEmployeeSchedule: (employeeId) => api.get(`/schedules/public/employee/${employeeId}`),
  updateEmployeeSchedule: (employeeId, scheduleData) => api.post(`/schedules/employee/${employeeId}`, scheduleData),
  deleteScheduleSlot: (slotId) => api.delete(`/schedules/slot/${slotId}`),
  getMySchedule: () => api.get('/schedules/my-schedule'),
}

// Subscription API
export const subscriptionAPI = {
  createSubscription: (shopId, subscriptionData) => api.post(`/subscriptions/shops/${shopId}`, subscriptionData),
  getSubscriptionDetails: (shopId) => api.get(`/subscriptions/shops/${shopId}`),
  validateSubscription: (shopId) => api.post(`/subscriptions/validate/${shopId}`),
  cancelSubscription: (shopId) => api.delete(`/subscriptions/shops/${shopId}`),
}

// Payment API
export const paymentAPI = {
  createPaymentIntent: (paymentData) => api.post('/payments/create-payment-intent', paymentData),
  confirmPaymentIntent: (paymentIntentId, paymentMethodId) => api.post('/payments/confirm-payment-intent', null, {
    params: { paymentIntentId, paymentMethodId }
  }),
  getPaymentStatus: (appointmentId) => api.get(`/payments/status/${appointmentId}`),
}

// Notification API
export const notificationAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  markAsRead: (id) => api.put(`/notifications/${id}/read`),
  markAllAsRead: () => api.put('/notifications/read-all'),
  autoMarkAsSeen: () => api.put('/notifications/auto-mark-seen'),
  getUnreadCount: () => api.get('/notifications/unread-count'),
}

// Analytics API
export const analyticsAPI = {
  getShopAnalytics: (shopId) => api.get(`/analytics/shop/${shopId}`),
  getOwnerAnalytics: () => api.get('/analytics/owner'),
  getAppointmentStats: (shopId, params) => api.get(`/analytics/shop/${shopId}/appointments`, { params }),
  getRevenueStats: (shopId, params) => api.get(`/analytics/shop/${shopId}/revenue`, { params }),
  getCustomerStats: (shopId, params) => api.get(`/analytics/shop/${shopId}/customers`, { params }),
}



export default api
