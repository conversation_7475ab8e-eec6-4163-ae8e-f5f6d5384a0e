/**
 * Utility functions for handling avatar URLs and display
 */

/**
 * Format avatar URL to include full localhost URL if it's a relative path
 * @param {string} avatarUrl - The avatar URL (could be relative or full)
 * @returns {string|null} - Formatted avatar URL or null if no avatar
 */
export const formatAvatarUrl = (avatarUrl) => {
  if (!avatarUrl) return null
  
  // If it's already a full URL (starts with http), return as is
  if (avatarUrl.startsWith('http')) {
    return avatarUrl
  }
  
  // If it's a relative path starting with /uploads/, add localhost
  if (avatarUrl.startsWith('/uploads/')) {
    return `http://localhost:8080${avatarUrl}`
  }
  
  // If it's an external service URL (like ui-avatars.com), return as is
  if (avatarUrl.includes('ui-avatars.com') || avatarUrl.includes('gravatar.com')) {
    return avatarUrl
  }
  
  // Default case - assume it's a relative path and add localhost
  return `http://localhost:8080${avatarUrl}`
}

/**
 * Generate initials from first and last name
 * @param {string} firstName - First name
 * @param {string} lastName - Last name
 * @returns {string} - Initials (e.g., "JS" for "John Smith")
 */
export const getInitials = (firstName, lastName) => {
  const first = firstName?.charAt(0)?.toUpperCase() || ''
  const last = lastName?.charAt(0)?.toUpperCase() || ''
  return `${first}${last}`
}

/**
 * Generate a placeholder avatar URL using ui-avatars.com service
 * @param {string} firstName - First name
 * @param {string} lastName - Last name
 * @param {string} backgroundColor - Background color (hex without #)
 * @param {string} textColor - Text color (hex without #)
 * @param {number} size - Avatar size in pixels
 * @returns {string} - Generated avatar URL
 */
export const generatePlaceholderAvatar = (
  firstName, 
  lastName, 
  backgroundColor = '6366f1', 
  textColor = 'ffffff', 
  size = 128
) => {
  const initials = getInitials(firstName, lastName)
  return `https://ui-avatars.com/api/?name=${initials}&background=${backgroundColor}&color=${textColor}&size=${size}`
}

/**
 * Get the best available avatar for a user/employee
 * Priority: uploaded avatar > placeholder avatar > initials fallback
 * @param {Object} user - User object with avatar, firstName, lastName
 * @returns {Object} - { avatarUrl, fallbackInitials }
 */
export const getBestAvatar = (user) => {
  if (!user) {
    return { avatarUrl: null, fallbackInitials: '?' }
  }
  
  const { avatar, firstName, lastName } = user
  const fallbackInitials = getInitials(firstName, lastName)
  
  // If user has an uploaded avatar, use it
  if (avatar) {
    return {
      avatarUrl: formatAvatarUrl(avatar),
      fallbackInitials
    }
  }
  
  // No uploaded avatar, return null to show initials fallback
  return {
    avatarUrl: null,
    fallbackInitials
  }
}

/**
 * Get avatar for employee (handles both direct avatar and user.avatar)
 * @param {Object} employee - Employee object
 * @returns {Object} - { avatarUrl, fallbackInitials }
 */
export const getEmployeeAvatar = (employee) => {
  if (!employee) {
    return { avatarUrl: null, fallbackInitials: '?' }
  }
  
  // Employee might have avatar directly or through user relationship
  const avatar = employee.avatar || employee.user?.avatar
  const firstName = employee.firstName || employee.user?.firstName
  const lastName = employee.lastName || employee.user?.lastName
  const name = employee.name || employee.fullName || `${firstName} ${lastName}`.trim()
  
  // Extract first and last name from full name if needed
  let extractedFirstName = firstName
  let extractedLastName = lastName
  
  if (!firstName && !lastName && name) {
    const nameParts = name.split(' ')
    extractedFirstName = nameParts[0] || ''
    extractedLastName = nameParts[nameParts.length - 1] || ''
  }
  
  const fallbackInitials = getInitials(extractedFirstName, extractedLastName)
  
  if (avatar) {
    return {
      avatarUrl: formatAvatarUrl(avatar),
      fallbackInitials
    }
  }
  
  return {
    avatarUrl: null,
    fallbackInitials
  }
}
