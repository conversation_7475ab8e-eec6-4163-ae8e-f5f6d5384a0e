// Mock axios
const mockAxiosInstance = {
  interceptors: {
    request: { use: jest.fn() },
    response: { use: jest.fn() },
  },
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
};

jest.mock('axios', () => ({
  create: jest.fn(() => mockAxiosInstance),
}));

// Mock the authStore
jest.mock('../../store/authStore', () => ({
  default: {
    getState: jest.fn(() => ({
      token: null,
      user: null,
      login: jest.fn(),
      logout: jest.fn(),
    })),
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('API Configuration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  test('should create axios instance', () => {
    // Import the api module to trigger axios.create
    require('../api');
    expect(require('axios').create).toHaveBeenCalled();
  });

  test('should load API module without errors', () => {
    expect(() => require('../api')).not.toThrow();
  });
});

describe('Auth API', () => {
  const mockApi = {
    post: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the api instance
    jest.doMock('../api', () => ({
      api: mockApi,
      authAPI: {
        login: (credentials) => mockApi.post('/auth/login', credentials),
        register: (userData) => mockApi.post('/auth/register', userData),
        registerOwner: (userData) => mockApi.post('/auth/register/owner', userData),
        logout: () => mockApi.post('/auth/logout'),
      },
    }));
  });

  test('should call login endpoint with credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'password123' };
    const mockResponse = { data: { token: 'mock-token' } };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.login(credentials);
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/login', credentials);
    expect(result).toBe(mockResponse);
  });

  test('should call register endpoint with user data', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
      lastName: 'Doe',
    };
    const mockResponse = {
      data: {
        token: 'mock-jwt-token',
        type: 'Bearer',
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        roles: ['ROLE_USER']
      }
    };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.register(userData);
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/register', userData);
    expect(result).toBe(mockResponse);
  });

  test('should call register owner endpoint with user data', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Jane',
      lastName: 'Doe',
    };
    const mockResponse = { data: { message: 'Owner registered successfully!' } };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.registerOwner(userData);
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/register/owner', userData);
    expect(result).toBe(mockResponse);
  });

  test('should call logout endpoint', async () => {
    const mockResponse = { data: { message: 'Logged out successfully' } };
    
    mockApi.post.mockResolvedValue(mockResponse);
    
    const { authAPI } = require('../api');
    const result = await authAPI.logout();
    
    expect(mockApi.post).toHaveBeenCalledWith('/auth/logout');
    expect(result).toBe(mockResponse);
  });
});

describe('User API', () => {
  test('should load User API module without errors', () => {
    expect(() => require('../api')).not.toThrow();
  });
});

describe('API Error Handling', () => {
  test('should handle API errors gracefully', () => {
    // Test that the API module loads without errors
    expect(() => require('../api')).not.toThrow();
  });
});
