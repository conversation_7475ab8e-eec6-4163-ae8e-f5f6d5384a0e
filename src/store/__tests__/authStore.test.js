import { renderHook, act } from '@testing-library/react';
import useAuthStore from '../authStore';

import { authAPI } from '../../lib/api';

// Mock the API module
jest.mock('../../lib/api', () => ({
  authAPI: {
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
    validateToken: jest.fn(),
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('Auth Store', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();

    // Reset the store state before each test
    const { result } = renderHook(() => useAuthStore());
    act(() => {
      result.current.logout();
    });
  });

  test('initial state should be correct', () => {
    const { result } = renderHook(() => useAuthStore());

    expect(result.current.user).toBeNull();
    expect(result.current.token).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.isLoading).toBe(false);
  });

  test('should initialize from localStorage if token exists', async () => {
    const mockUser = {
      id: '123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    };
    const mockToken = 'valid-jwt-token';

    // Mock Zustand persist storage format
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'auth-storage') {
        return JSON.stringify({
          state: {
            user: mockUser,
            token: mockToken,
            isAuthenticated: true
          },
          version: 1
        });
      }
      return null;
    });

    // Mock validateToken to return true for this test
    authAPI.validateToken.mockResolvedValue({ data: { valid: true } });

    const { result } = renderHook(() => useAuthStore());

    // First login to set up the store state
    act(() => {
      result.current.login(mockUser, mockToken);
    });

    // Mock isTokenValid to return true for this test
    const originalIsTokenValid = result.current.isTokenValid;
    result.current.isTokenValid = jest.fn().mockReturnValue(true);

    // Call initializeAuth and wait for it to complete
    await act(async () => {
      await result.current.initializeAuth();
    });

    // The user should be authenticated immediately (since token is not expired)
    expect(result.current.user).toEqual(mockUser);
    expect(result.current.token).toBe(mockToken);
    expect(result.current.isAuthenticated).toBe(true);

    // Restore original function
    result.current.isTokenValid = originalIsTokenValid;
  });

  test('should handle login successfully', async () => {
    const mockUser = {
      id: '123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    };
    const mockToken = 'mock-jwt-token';

    const { result } = renderHook(() => useAuthStore());

    act(() => {
      result.current.login(mockUser, mockToken);
    });

    expect(result.current.user).toEqual(mockUser);
    expect(result.current.token).toBe(mockToken);
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.isLoading).toBe(false);
  });

  test('should handle logout correctly', () => {
    const { result } = renderHook(() => useAuthStore());

    // First login
    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, 'mock-token');
    });

    // Then logout
    act(() => {
      result.current.logout();
    });

    expect(result.current.user).toBeNull();
    expect(result.current.token).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  test('should set loading state during async operations', async () => {
    const { result } = renderHook(() => useAuthStore());

    act(() => {
      result.current.setLoading(true);
    });

    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.setLoading(false);
    });

    expect(result.current.isLoading).toBe(false);
  });

  // Removed error handling test since the new auth store doesn't have setError/clearError methods

  test('should update user profile', () => {
    const { result } = renderHook(() => useAuthStore());

    // First login
    act(() => {
      result.current.login({
        id: '123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
      }, 'mock-token');
    });

    const updatedUserData = {
      firstName: 'Jane',
      lastName: 'Smith',
      phone: '+1234567890',
    };

    act(() => {
      result.current.updateUser(updatedUserData);
    });

    const expectedUser = {
      id: '123',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
      phone: '+1234567890',
    };

    expect(result.current.user).toEqual(expectedUser);
  });

  test('should check if user has specific role', () => {
    const { result } = renderHook(() => useAuthStore());

    act(() => {
      result.current.login({
        id: '123',
        email: '<EMAIL>',
        roles: ['ROLE_USER', 'ROLE_OWNER'],
      }, 'mock-token');
    });

    expect(result.current.hasRole('USER')).toBe(true);
    expect(result.current.hasRole('OWNER')).toBe(true);
    expect(result.current.hasRole('ADMIN')).toBe(false);
  });

  test('should return false for role check when not authenticated', () => {
    const { result } = renderHook(() => useAuthStore());

    expect(result.current.hasRole('USER')).toBe(false);
  });

  test('should handle token refresh', async () => {
    const newToken = 'new-jwt-token';
    const userData = { id: '123', email: '<EMAIL>', firstName: 'Test', lastName: 'User', roles: ['ROLE_USER'] };

    authAPI.refreshToken = jest.fn().mockResolvedValue({
      data: {
        token: newToken,
        id: userData.id,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        roles: userData.roles
      },
    });

    const { result } = renderHook(() => useAuthStore());

    // First login
    act(() => {
      result.current.login(userData, 'old-token');
    });

    await act(async () => {
      const success = await result.current.refreshToken();
      expect(success).toBe(true);
    });

    expect(result.current.token).toBe(newToken);
  });

  test('should handle token refresh failure', async () => {
    // Mock a 401 error (authentication failure)
    authAPI.refreshToken.mockRejectedValue({
      response: { status: 401 },
      message: 'Token refresh failed'
    });

    const { result } = renderHook(() => useAuthStore());

    // First login
    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, 'old-token');
    });

    await act(async () => {
      const success = await result.current.refreshToken();
      expect(success).toBe(false);
    });

    // Should logout on 401 refresh failure
    expect(result.current.user).toBeNull();
    expect(result.current.token).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  test('should not logout on network error during refresh', async () => {
    // Mock a network error (no response)
    authAPI.refreshToken.mockRejectedValue({
      code: 'NETWORK_ERROR',
      message: 'Network error'
    });

    const { result } = renderHook(() => useAuthStore());

    // First login
    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, 'old-token');
    });

    await act(async () => {
      const success = await result.current.refreshToken();
      expect(success).toBe(false);
    });

    // Should NOT logout on network error
    expect(result.current.user).not.toBeNull();
    expect(result.current.token).toBe('old-token');
    expect(result.current.isAuthenticated).toBe(true);
  });

  test('should validate token successfully', async () => {
    // Mock the API call first
    authAPI.validateToken.mockResolvedValue({
      data: { valid: true },
    });

    const { result } = renderHook(() => useAuthStore());

    // Create a valid JWT token for testing
    const validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lp-38RNpyBo3_eFbXdKXYjKqB_9Wy8JLwNGYKJGlE3s';

    // Login first to set a token in the store
    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, validToken);
    });

    await act(async () => {
      const isValid = await result.current.validateToken();
      expect(isValid).toBe(true);
    });

    expect(authAPI.validateToken).toHaveBeenCalled();
  });

  test('should handle token validation failure', async () => {
    authAPI.validateToken.mockRejectedValue(new Error('Validation failed'));

    const { result } = renderHook(() => useAuthStore());

    // Set token in localStorage
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'token') return 'invalid-token';
      return null;
    });

    await act(async () => {
      const isValid = await result.current.validateToken();
      expect(isValid).toBe(false);
    });
  });

  test('should validate token expiration', () => {
    const { result } = renderHook(() => useAuthStore());

    // Mock a token that expires in the future
    const futureToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lp-38RNpyBo3_eFbXdKXYjKqB_9Wy8JLwNGYKJGlE3s';

    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, futureToken);
    });

    expect(result.current.isTokenValid()).toBe(true);
  });

  test('should detect expired token', () => {
    const { result } = renderHook(() => useAuthStore());

    // Mock a token that has already expired
    const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';

    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, expiredToken);
    });

    expect(result.current.isTokenValid()).toBe(false);
  });

  test('should handle invalid token format', () => {
    const { result } = renderHook(() => useAuthStore());

    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, 'invalid-token');
    });

    expect(result.current.isTokenValid()).toBe(false);
  });

  test('should persist state across hook re-renders', () => {
    const { result, rerender } = renderHook(() => useAuthStore());

    act(() => {
      result.current.login({ id: '123', email: '<EMAIL>' }, 'mock-token');
    });

    rerender();

    expect(result.current.user).toEqual({ id: '123', email: '<EMAIL>' });
    expect(result.current.token).toBe('mock-token');
    expect(result.current.isAuthenticated).toBe(true);
  });

  test('should handle concurrent login attempts', async () => {
    const { result } = renderHook(() => useAuthStore());

    const userData1 = { id: '123', email: '<EMAIL>' };
    const userData2 = { id: '456', email: '<EMAIL>' };

    act(() => {
      // Simulate concurrent login attempts
      result.current.login(userData1, 'token1');
      result.current.login(userData2, 'token2');
    });

    // The last login should win
    expect(result.current.user.email).toBe('<EMAIL>');
    expect(result.current.token).toBe('token2');
  });
});
