import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authAPI } from '../lib/api'

// Auth store for managing authentication state

const useAuthStore = create(
  persist(
    (set, get) => {


      return {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        _hasHydrated: false,

      login: (userData, token) => {
        // Format avatar URL if it exists and is a relative path
        let formattedUserData = { ...userData }
        if (formattedUserData.avatar && formattedUserData.avatar.startsWith('/uploads/')) {
          formattedUserData.avatar = `http://localhost:8080${formattedUserData.avatar}`
        }

        set({
          user: formattedUserData,
          token,
          isAuthenticated: true,
        })
      },

      updateUser: (userData) => {
        // Format avatar URL if it exists and is a relative path
        let formattedUserData = { ...userData }
        if (formattedUserData.avatar && formattedUserData.avatar.startsWith('/uploads/')) {
          formattedUserData.avatar = `http://localhost:8080${formattedUserData.avatar}`
        }

        set(state => ({
          user: { ...state.user, ...formattedUserData }
        }))
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
        // Remove manual localStorage operations - Zustand persist handles this

        // Reset notification store when logging out
        try {
          const { reset } = require('./notificationStore').default.getState()
          reset()
        } catch (error) {
          console.error('Error resetting notification store:', error)
        }
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      // Initialize auth state from persisted store with validation
      initializeAuth: async () => {

        // Clean up any manual localStorage entries to prevent conflicts
        const manualToken = localStorage.getItem('token')
        const manualUser = localStorage.getItem('user')
        if (manualToken || manualUser) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
        }

        // Check what's in localStorage before we start
        const authStorage = localStorage.getItem('auth-storage')

        const state = get()
        const { token, user, isAuthenticated } = state

        if (token && user && isAuthenticated) {
          try {
            // First, do a client-side token expiry check
            const isTokenExpired = !get().isTokenValid()

            if (isTokenExpired) {
              // Token is expired, try to refresh
              const refreshed = await get().refreshToken()
              if (!refreshed) {
                // Don't logout immediately on initialization failure
                // Let the API interceptor handle it when actual API calls are made
                return
              }
            } else {
              // Token appears valid client-side, now test backend validation

              try {
                const isValid = await get().validateToken()
                // Token validation completed
              } catch (error) {
                // Don't logout on initialization validation failure
                // The API interceptor will handle token refresh when needed
              }
            }

          } catch (error) {
            console.error('Auth initialization failed:', error)
            // Don't logout during initialization unless it's a clear auth failure
            // Let the API interceptor handle token refresh when actual API calls are made
          }
        }
      },

      // Validate current token
      validateToken: async () => {
        const { token } = get()
        if (!token) {
          return false
        }


        // First check client-side validity
        const clientSideValid = get().isTokenValid()

        if (!clientSideValid) {
          return false
        }

        try {
          const response = await authAPI.validateToken()

          if (response.data.valid) {
            // Update user data if validation returns updated user info
            if (response.data.user) {
              // Format avatar URL if it's a relative path
              let formattedUser = { ...response.data.user }
              if (formattedUser.avatar && formattedUser.avatar.startsWith('/uploads/')) {
                formattedUser.avatar = `http://localhost:8080${formattedUser.avatar}`
              }
              set({ user: formattedUser })
            }
            return true
          }
          return false
        } catch (error) {
          console.error('Token validation failed:', error)

          // If it's a network error, fall back to client-side validation
          if (error.code === 'NETWORK_ERROR' || !error.response) {
            return get().isTokenValid()
          }

          return false
        }
      },

      // Refresh token
      refreshToken: async () => {
        const { token: currentToken, user: currentUser } = get()
        if (!currentToken) {
          return false
        }


        try {
          const response = await authAPI.refreshToken()
          const { token, id, email, firstName, lastName, avatar, roles } = response.data

          // Format avatar URL properly
          let formattedAvatar = avatar
          if (formattedAvatar && formattedAvatar.startsWith('/uploads/')) {
            formattedAvatar = `http://localhost:8080${formattedAvatar}`
          }

          // Update store - Zustand persist will handle localStorage
          set({
            user: { id, email, firstName, lastName, avatar: formattedAvatar, roles },
            token,
            isAuthenticated: true,
          })

          return true
        } catch (error) {
          console.error('Token refresh failed:', error)

          // Only logout for authentication errors, not network errors
          if (error.response?.status === 401 || error.response?.status === 403) {
            get().logout()
          } else if (error.code === 'NETWORK_ERROR' || !error.response) {
            // Don't logout on network errors - user might be offline temporarily
          }

          return false
        }
      },

      // Check if token is expired (client-side check)
      isTokenValid: () => {
        const { token } = get()
        if (!token) {
          return false
        }

        try {
          // Decode JWT payload (without verification - just for expiry check)
          const payload = JSON.parse(atob(token.split('.')[1]))
          const currentTime = Date.now() / 1000
          const timeUntilExpiry = payload.exp - currentTime

          return payload.exp > currentTime
        } catch (error) {
          console.error('Error checking token validity:', error)
          return false
        }
      },

      // Check if user has specific role
      hasRole: (role) => {
        const { user } = get()
        return user?.roles?.includes(`ROLE_${role}`) || false
      },

      // Check if user is owner
      isOwner: () => get().hasRole('OWNER'),

      // Check if user is employee
      isEmployee: () => get().hasRole('EMPLOYEE'),

      // Check if user is regular user
      isUser: () => get().hasRole('USER'),

      // Update user data (for refreshing user info without full re-auth)
      setUser: (userData) => {
        set({ user: userData })
      },

      // Mark store as hydrated
      markAsHydrated: () => {
        set({ _hasHydrated: true })
      },

      }
    },
    {
      name: 'auth-storage',
      version: 1, // Add version to force refresh if needed
      partialize: (state) => {
        return {
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
        }
      },
      merge: (persistedState, currentState) => {
        // Format avatar URL during rehydration
        if (persistedState?.user?.avatar && persistedState.user.avatar.startsWith('/uploads/')) {
          persistedState.user.avatar = `http://localhost:8080${persistedState.user.avatar}`
        }

        return {
          ...currentState,
          ...persistedState,
        }
      },
      onRehydrateStorage: () => {
        return (state, error) => {

          if (error) {
            console.error('Auth store rehydration failed:', error)
            return
          }
        }
      },
    }
  )
)

// Mark as hydrated after rehydration is complete
// Use a longer delay to ensure Zustand persist has finished
setTimeout(() => {
  useAuthStore.getState().markAsHydrated()
}, 500) // Increased delay

export default useAuthStore
